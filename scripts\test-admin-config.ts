#!/usr/bin/env tsx

/**
 * Test script to verify admin configuration
 * This script helps verify that the SUPABASE_SERVICE_ROLE_KEY is properly configured
 * and that the admin client can access Supabase auth functions.
 */

import * as dotenv from 'dotenv';
import { resolve } from 'path';

// Load environment variables
dotenv.config({ path: resolve(__dirname, '../.env.local') });

import { isAdminClientConfigured, createAdminClient } from '../utils/supabase/admin';

async function testAdminConfig() {
  console.log('🔧 Testing Admin Configuration...\n');

  try {
    // Test 1: Check if environment variables are configured
    console.log('📋 Test 1: Checking environment variables...');
    
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl) {
      console.log('❌ NEXT_PUBLIC_SUPABASE_URL is not configured');
      return;
    } else {
      console.log('✅ NEXT_PUBLIC_SUPABASE_URL is configured');
    }
    
    if (!serviceRoleKey) {
      console.log('❌ SUPABASE_SERVICE_ROLE_KEY is not configured');
      console.log('   Add this to your .env.local file to enable real email addresses in admin panel');
      return;
    } else {
      console.log('✅ SUPABASE_SERVICE_ROLE_KEY is configured');
    }

    // Test 2: Check if admin client can be created
    console.log('\n📋 Test 2: Testing admin client creation...');
    
    if (!isAdminClientConfigured()) {
      console.log('❌ Admin client configuration check failed');
      return;
    }
    
    const adminClient = createAdminClient();
    console.log('✅ Admin client created successfully');

    // Test 3: Test basic auth admin functionality
    console.log('\n📋 Test 3: Testing auth admin access...');
    
    try {
      // Try to list users (this will fail if the service role key is invalid)
      const { data, error } = await adminClient.auth.admin.listUsers({
        page: 1,
        perPage: 1
      });
      
      if (error) {
        console.log('❌ Auth admin access failed:', error.message);
        console.log('   Please verify your SUPABASE_SERVICE_ROLE_KEY is correct');
        return;
      }
      
      console.log('✅ Auth admin access successful');
      console.log(`   Found ${data.users.length} user(s) in first page`);
      
    } catch (err) {
      console.log('❌ Auth admin access failed:', err);
      return;
    }

    console.log('\n🎉 All tests passed! Admin configuration is working correctly.');
    console.log('   Real email addresses will be displayed in the admin user management panel.');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the test
testAdminConfig()
  .then(() => {
    process.exit(0);
  })
  .catch((error) => {
    console.error('Test script failed:', error);
    process.exit(1);
  });
