# Revision - Next.js E-commerce Platform

A modern e-commerce application built with Next.js 15, TypeScript, Tailwind CSS, and Shadcn UI, featuring Square payment integration and Supabase authentication.

## Features

- **Next.js 15** with App Router and Server Components
- **TypeScript** for type safety and improved developer experience
- **Tailwind CSS** for responsive, utility-first styling
- **Shadcn UI** for beautiful, accessible components
- **Supabase Authentication** for user management and protected routes
- **Square Payment Integration** for secure checkout processing
- **Shopping Cart** with persistent storage
- **Mobile-first responsive design**
- **Cypress Testing** for payment flows and user interactions

## Getting Started

### Prerequisites

- Node.js 18.17 or later
- Supabase account (for authentication)
- Square Developer account (for payment processing)

### Environment Setup

1. Create a `.env.local` file with the following variables:

```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
SQUARE_ACCESS_TOKEN=your_square_access_token
SQUARE_ENVIRONMENT=sandbox
NEXT_PUBLIC_SQUARE_APPLICATION_ID=your_square_application_id
NEXT_PUBLIC_SQUARE_LOCATION_ID=your_square_location_id
```

### Installation

1. Clone the repository:

```bash
git clone https://github.com/yourusername/revision.git
cd revision
```

2. Install dependencies:

```bash
npm install
```

3. Run the development server:

```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser to see the result.

## Project Structure

```
├── app/                  # Next.js App Router
│   ├── api/              # API routes including payment processing
│   ├── auth/             # Authentication routes
│   ├── products/         # Product pages
│   ├── cart/             # Shopping cart
│   ├── checkout/         # Checkout flow
│   ├── globals.css       # Global styles
│   └── layout.tsx        # Root layout with providers
├── components/           # React components
│   ├── ui/               # Shadcn UI components
│   └── ...               # App-specific components
├── context/              # React Context providers
│   └── cart-context.tsx  # Shopping cart state management
├── cypress/              # End-to-end tests
├── lib/                  # Utility functions
├── utils/                # Helper utilities
│   └── supabase/         # Supabase client utilities
└── public/               # Static assets
```

## Key Features

### Authentication

The application uses Supabase Auth for user authentication:
- Login/signup flows with email/password
- Protected routes via middleware
- User profile management

### Admin Panel

The application includes an admin panel for managing users, orders, and inventory:
- **User Management**: View and manage customer accounts
- **Order Management**: Track and update order statuses
- **Inventory Management**: Monitor and update product stock levels

#### Admin Configuration

To enable real email addresses in the admin user management:

1. **Get your Supabase Service Role Key**:
   - Go to your Supabase project dashboard
   - Navigate to Settings > API
   - Copy the `service_role` key (not the `anon` key)

2. **Add to environment variables**:
   ```
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
   ```

3. **Security Note**: The service role key has admin privileges and should:
   - Only be used server-side (never exposed to client)
   - Be kept secure and not committed to version control
   - Only be used for admin operations

Without the service role key, the admin panel will show placeholder email addresses for privacy.

### Shopping Cart

Cart functionality is implemented using React Context:
- Add/remove products
- Update quantities
- Persistent storage using localStorage
- Real-time total calculation

### Payment Processing

Secure payment processing with Square:
- Client-side card tokenization
- Server-side payment processing
- Sandbox testing environment
- Order confirmation flow

## Testing

Run Cypress tests to verify application functionality:

```bash
# Open Cypress test runner
npm run cypress:open

# Run tests headlessly
npm run cypress:run
```

For payment testing, refer to `TESTING.md` for test card numbers and testing procedures.

## Development Guidelines

- Use functional components with TypeScript interfaces
- Follow the mobile-first approach for responsive design
- Prefer Server Components when possible
- Run `npm run lint` before committing changes

## Learn More

- [Next.js Documentation](https://nextjs.org/docs)
- [Supabase Documentation](https://supabase.com/docs)
- [Square Developer Documentation](https://developer.squareup.com/docs)
- [Shadcn UI Documentation](https://ui.shadcn.com)