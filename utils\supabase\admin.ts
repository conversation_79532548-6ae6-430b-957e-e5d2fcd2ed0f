import { createClient } from '@supabase/supabase-js'

/**
 * Creates a Supabase client with admin privileges using the service role key.
 * This client can access auth.admin functions to retrieve user data.
 * 
 * SECURITY NOTE: This should only be used server-side and never exposed to the client.
 */
export function createAdminClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl) {
    throw new Error('NEXT_PUBLIC_SUPABASE_URL environment variable is required')
  }

  if (!serviceRoleKey) {
    throw new Error('SUPABASE_SERVICE_ROLE_KEY environment variable is required for admin operations')
  }

  return createClient(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

/**
 * Checks if admin client is properly configured
 */
export function isAdminClientConfigured(): boolean {
  return !!(process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.SUPABASE_SERVICE_ROLE_KEY)
}

/**
 * Fetches user data from Supabase auth including email addresses
 * @param userIds Array of user IDs to fetch
 * @returns Map of user ID to user data
 */
export async function fetchUsersFromAuth(userIds: string[]): Promise<Map<string, { email: string; created_at: string; last_sign_in_at?: string }>> {
  if (!isAdminClientConfigured()) {
    throw new Error('Admin client not configured. Please set SUPABASE_SERVICE_ROLE_KEY environment variable.')
  }

  const adminClient = createAdminClient()
  const userMap = new Map<string, { email: string; created_at: string; last_sign_in_at?: string }>()

  try {
    // Fetch users in batches to avoid overwhelming the API
    const batchSize = 50
    for (let i = 0; i < userIds.length; i += batchSize) {
      const batch = userIds.slice(i, i + batchSize)
      
      // Get users by ID
      for (const userId of batch) {
        try {
          const { data: user, error } = await adminClient.auth.admin.getUserById(userId)
          
          if (error) {
            console.warn(`Failed to fetch user ${userId}:`, error.message)
            continue
          }

          if (user?.user) {
            userMap.set(userId, {
              email: user.user.email || '',
              created_at: user.user.created_at,
              last_sign_in_at: user.user.last_sign_in_at || undefined
            })
          }
        } catch (err) {
          console.warn(`Error fetching user ${userId}:`, err)
          continue
        }
      }
    }

    return userMap
  } catch (error) {
    console.error('Error fetching users from auth:', error)
    throw new Error('Failed to fetch user data from authentication service')
  }
}
