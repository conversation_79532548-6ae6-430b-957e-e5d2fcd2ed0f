# Admin Panel Real Email Configuration

This document explains how to configure the admin panel to display real customer email addresses instead of placeholder data.

## Overview

The admin user management panel has been updated to fetch real email addresses from Supabase Auth when properly configured. Without configuration, it will safely fall back to placeholder emails for privacy.

## What Was Changed

### 1. New Admin Client Utility (`utils/supabase/admin.ts`)
- Created a secure admin client that uses the Supabase service role key
- Implements batch fetching of user data from Supabase Auth
- Includes proper error handling and security checks
- Only works server-side to protect the service role key

### 2. Updated Admin Users API (`app/api/admin/users/route.ts`)
- Modified to fetch real user emails when service role key is configured
- Enhanced search functionality to include email addresses
- Maintains backward compatibility with placeholder emails
- Provides clear feedback about configuration status

### 3. Enhanced Frontend (`app/admin/users/page.tsx`)
- Added visual indicators showing whether real emails are enabled
- Displays configuration notices to administrators
- Improved user experience with clear status messages

### 4. Configuration Testing (`scripts/test-admin-config.ts`)
- Created a test script to verify admin configuration
- Validates environment variables and Supabase connectivity
- Provides clear feedback on configuration issues

## Setup Instructions

### Step 1: Get Your Supabase Service Role Key

1. Go to your Supabase project dashboard
2. Navigate to **Settings** > **API**
3. Copy the **service_role** key (NOT the anon key)
4. Keep this key secure - it has admin privileges

### Step 2: Add Environment Variable

Add the following to your `.env.local` file:

```bash
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

### Step 3: Test Configuration

Run the test script to verify everything is working:

```bash
npm run test-admin
```

This will:
- Check if environment variables are configured
- Test admin client creation
- Verify Supabase Auth access
- Confirm real emails will be displayed

### Step 4: Restart Your Application

After adding the environment variable, restart your development server:

```bash
npm run dev
```

## Security Considerations

### ✅ What We Did Right

1. **Server-Side Only**: Service role key is only used in server-side API routes
2. **Environment Variables**: Key is stored securely in environment variables
3. **Error Handling**: Graceful fallback to placeholder emails if configuration fails
4. **No Client Exposure**: Admin client is never exposed to the browser
5. **Proper Validation**: Configuration is validated before use

### ⚠️ Important Security Notes

1. **Never commit** the service role key to version control
2. **Only use** the service role key for admin operations
3. **Restrict access** to the admin panel to authorized users only
4. **Monitor usage** of admin functions in production
5. **Rotate keys** periodically for security

## How It Works

### Without Service Role Key
- Admin panel shows placeholder emails like `<EMAIL>`
- Search works on names and locations only
- Yellow notice displayed explaining configuration needed

### With Service Role Key
- Admin panel fetches and displays real user email addresses
- Search includes email addresses in results
- Green notice confirms real emails are enabled
- Last login times are accurately displayed

## Troubleshooting

### "Admin client not configured" Error
- Check that `SUPABASE_SERVICE_ROLE_KEY` is set in `.env.local`
- Verify the key is the service role key, not the anon key
- Restart your development server after adding the variable

### "Auth admin access failed" Error
- Verify the service role key is correct
- Check your Supabase project settings
- Ensure your Supabase project is active

### Users Not Loading
- Check browser console for error messages
- Verify admin authentication is working
- Run `npm run test-admin` to diagnose issues

## Testing

### Manual Testing
1. Navigate to `/admin/users` (requires admin access)
2. Check the notice at the top of the page
3. Verify email addresses are real (not placeholder format)
4. Test search functionality with email addresses

### Automated Testing
```bash
npm run test-admin
```

## Rollback

If you need to disable real email fetching:
1. Remove or comment out `SUPABASE_SERVICE_ROLE_KEY` from `.env.local`
2. Restart your application
3. The system will automatically fall back to placeholder emails

## Support

If you encounter issues:
1. Run the test script: `npm run test-admin`
2. Check the browser console for error messages
3. Verify your Supabase project configuration
4. Ensure you have the correct service role key
