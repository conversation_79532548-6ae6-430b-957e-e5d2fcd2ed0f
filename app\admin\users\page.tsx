"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Search, Filter, UserPlus, Mail, MoreHorizontal, AlertCircle, Loader2 } from "lucide-react";

interface UserProfile {
  firstName: string | null;
  lastName: string | null;
  city: string | null;
  state: string | null;
  country: string | null;
  isAdmin: boolean;
  createdAt: string | null;
  updatedAt: string | null;
}

interface User {
  id: string;
  email: string;
  createdAt: string;
  last_sign_in_at?: string | null;
  created_at?: string;
  profile: UserProfile | null;
}

interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

interface UserStats {
  totalUsers: number;
  activeUsers: number;
  newSignupsThisWeek: number;
}

export default function UsersManagement() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [debouncedSearch, setDebouncedSearch] = useState("");
  const [notice, setNotice] = useState<string | null>(null);
  const [usingRealEmails, setUsingRealEmails] = useState(false);
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });
  const [stats, setStats] = useState<UserStats>({
    totalUsers: 0,
    activeUsers: 0,
    newSignupsThisWeek: 0,
  });

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchQuery);
      setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page on search
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Fetch users data
  useEffect(() => {
    fetchUsers();
  }, [pagination.page, debouncedSearch]); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(debouncedSearch && { search: debouncedSearch }),
      });

      const response = await fetch(`/api/admin/users?${params}`);
      
      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('You are not authorized to view this page');
        }
        if (response.status === 403) {
          throw new Error('Admin access required');
        }
        throw new Error('Failed to fetch users');
      }

      const data = await response.json();
      setUsers(data.users);
      setPagination(data.pagination);
      setStats(data.stats);
      setNotice(data.notice || null);
      setUsingRealEmails(data.usingRealEmails || false);

      // Show notice if present
      if (data.notice) {
        console.info('Admin Users Notice:', data.notice);
      }
    } catch (err) {
      console.error('Error fetching users:', err);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  };

  const getUserStatus = (user: User) => {
    if (!user.last_sign_in_at) return 'Inactive';
    
    const lastSignIn = new Date(user.last_sign_in_at);
    const daysSinceLastSignIn = Math.floor((Date.now() - lastSignIn.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysSinceLastSignIn <= 7) return 'Active';
    if (daysSinceLastSignIn <= 30) return 'Active';
    return 'Inactive';
  };

  const getUserRole = (user: User) => {
    return user.profile?.isAdmin ? 'Admin' : 'Customer';
  };

  const getUserName = (user: User) => {
    if (!user.profile) return user.email;
    const { firstName, lastName } = user.profile;
    if (firstName || lastName) {
      return `${firstName || ''} ${lastName || ''}`.trim();
    }
    return user.email;
  };

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  if (error) {
    return (
      <div className="flex items-center justify-center h-96">
        <Card className="p-6 bg-red-500/10 border-red-500/20">
          <div className="flex items-center gap-3">
            <AlertCircle className="h-5 w-5 text-red-500" />
            <div>
              <h3 className="font-medium text-red-500">Error</h3>
              <p className="text-sm text-muted-foreground">{error}</p>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h1 className="text-2xl font-semibold">Users Management</h1>
        <Button size="sm" className="flex items-center gap-1">
          <UserPlus className="h-4 w-4" />
          <span>Add User</span>
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-6 bg-card">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-lg font-medium">Total Users</h3>
          </div>
          <p className="text-3xl font-bold">{stats.totalUsers}</p>
          <p className="text-sm text-muted-foreground mt-1">All registered users</p>
        </Card>
        
        <Card className="p-6 bg-card">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-lg font-medium">Active Users</h3>
          </div>
          <p className="text-3xl font-bold">{stats.activeUsers}</p>
          <p className="text-sm text-muted-foreground mt-1">
            {stats.totalUsers > 0 ? `${((stats.activeUsers / stats.totalUsers) * 100).toFixed(1)}% of total users` : '0% of total users'}
          </p>
        </Card>
        
        <Card className="p-6 bg-card">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-lg font-medium">New Signups</h3>
          </div>
          <p className="text-3xl font-bold">{stats.newSignupsThisWeek}</p>
          <p className="text-sm text-muted-foreground mt-1">This week</p>
        </Card>
      </div>

      {notice && (
        <Card className={`p-4 ${usingRealEmails ? 'bg-green-500/10 border-green-500/20' : 'bg-yellow-500/10 border-yellow-500/20'}`}>
          <div className="flex items-center gap-3">
            <AlertCircle className={`h-5 w-5 ${usingRealEmails ? 'text-green-500' : 'text-yellow-500'}`} />
            <div>
              <h3 className={`font-medium ${usingRealEmails ? 'text-green-500' : 'text-yellow-500'}`}>
                {usingRealEmails ? 'Real Emails Enabled' : 'Configuration Notice'}
              </h3>
              <p className="text-sm text-muted-foreground">{notice}</p>
              {!usingRealEmails && (
                <p className="text-sm text-muted-foreground mt-1">
                  Run <code className="bg-muted px-1 rounded">npm run test-admin</code> to verify your configuration.
                </p>
              )}
            </div>
          </div>
        </Card>
      )}

      <Card className="bg-card">
        <div className="p-4 border-b border-neutral-800 flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search by name, email, or location..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-background border border-input rounded-md focus:outline-none focus:ring-1 focus:ring-ring"
            />
          </div>
          <Button variant="outline" size="sm" className="flex items-center gap-1">
            <Filter className="h-4 w-4" />
            <span>Filter</span>
          </Button>
        </div>
        <div className="overflow-x-auto">
          {loading ? (
            <div className="p-8 text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2 text-muted-foreground" />
              <p className="text-muted-foreground">Loading users...</p>
            </div>
          ) : users.length === 0 ? (
            <div className="p-8 text-center text-muted-foreground">
              {debouncedSearch ? 'No users found matching your search' : 'No users found'}
            </div>
          ) : (
            <table className="w-full">
              <thead>
                <tr className="text-left text-xs text-muted-foreground border-b border-neutral-800">
                  <th className="px-4 py-3">Name</th>
                  <th className="px-4 py-3">Email</th>
                  <th className="px-4 py-3">Role</th>
                  <th className="px-4 py-3">Status</th>
                  <th className="px-4 py-3">Location</th>
                  <th className="px-4 py-3">Last Login</th>
                  <th className="px-4 py-3">Joined</th>
                  <th className="px-4 py-3 text-right">Actions</th>
                </tr>
              </thead>
              <tbody>
                {users.map((user) => {
                  const status = getUserStatus(user);
                  const role = getUserRole(user);
                  const name = getUserName(user);
                  const location = user.profile 
                    ? [user.profile.city, user.profile.state, user.profile.country]
                        .filter(Boolean)
                        .join(', ') || '-'
                    : '-';

                  return (
                    <tr key={user.id} className="border-b border-neutral-800 last:border-0 hover:bg-muted/5">
                      <td className="px-4 py-3 text-sm font-medium">{name}</td>
                      <td className="px-4 py-3 text-sm">{user.email}</td>
                      <td className="px-4 py-3 text-sm">
                        <span className={`inline-block px-2 py-1 rounded-full text-xs ${
                          role === "Admin" 
                            ? "bg-purple-500/10 text-purple-500" 
                            : "bg-neutral-500/10 text-neutral-400"
                        }`}>
                          {role}
                        </span>
                      </td>
                      <td className="px-4 py-3 text-sm">
                        <span className={`inline-block px-2 py-1 rounded-full text-xs ${
                          status === "Active" 
                            ? "bg-green-500/10 text-green-500" 
                            : "bg-neutral-500/10 text-neutral-400"
                        }`}>
                          {status}
                        </span>
                      </td>
                      <td className="px-4 py-3 text-sm text-muted-foreground">{location}</td>
                      <td className="px-4 py-3 text-sm">{formatDate(user.last_sign_in_at || null)}</td>
                      <td className="px-4 py-3 text-sm">{formatDate(user.created_at || user.createdAt)}</td>
                      <td className="px-4 py-3 text-sm text-right">
                        <div className="flex items-center justify-end space-x-2">
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Mail className="h-4 w-4" />
                            <span className="sr-only">Email</span>
                          </Button>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">More</span>
                          </Button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          )}
        </div>
        <div className="p-4 border-t border-neutral-800 flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing <span className="font-medium">{users.length > 0 ? ((pagination.page - 1) * pagination.limit) + 1 : 0}</span> to{" "}
            <span className="font-medium">{Math.min(pagination.page * pagination.limit, pagination.total)}</span> of{" "}
            <span className="font-medium">{pagination.total}</span> users
          </div>
          <div className="flex items-center space-x-2">
            <Button 
              variant="outline" 
              size="sm" 
              disabled={pagination.page <= 1 || loading}
              onClick={() => handlePageChange(pagination.page - 1)}
            >
              Previous
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              disabled={pagination.page >= pagination.totalPages || loading}
              onClick={() => handlePageChange(pagination.page + 1)}
            >
              Next
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
}