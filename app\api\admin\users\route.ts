import { NextResponse } from 'next/server';
import { requireAdmin } from '@/src/db/auth';
import { db } from '@/src/db';
import { profiles } from '@/src/db/schema';
import { like, or, desc, sql } from 'drizzle-orm';
import { fetchUsersFromAuth, isAdminClientConfigured } from '@/utils/supabase/admin';

interface UserData {
  id: string;
  email: string;
  createdAt: string;
  last_sign_in_at?: string | null;
  created_at?: string;
  profile: {
    firstName: string | null;
    lastName: string | null;
    city: string | null;
    state: string | null;
    country: string | null;
    isAdmin: boolean;
    createdAt: string | null;
    updatedAt: string | null;
  } | null;
}

export async function GET(request: Request) {
  try {
    // Verify admin authentication
    await requireAdmin();

    // Parse URL parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const offset = (page - 1) * limit;

    // Build search filter for profiles
    const searchFilter = search
      ? or(
          like(profiles.firstName, `%${search}%`),
          like(profiles.lastName, `%${search}%`),
          like(profiles.city, `%${search}%`)
        )
      : undefined;

    // Get total count of profiles
    const totalCountQuery = db
      .select({ count: sql<number>`count(*)` })
      .from(profiles);

    if (searchFilter) {
      totalCountQuery.where(searchFilter);
    }

    const [{ count: totalUsers }] = await totalCountQuery;

    // Fetch profiles with pagination
    const profilesQuery = db
      .select({
        id: profiles.id,
        firstName: profiles.firstName,
        lastName: profiles.lastName,
        city: profiles.city,
        state: profiles.state,
        country: profiles.country,
        isAdmin: profiles.isAdmin,
        createdAt: profiles.createdAt,
        updatedAt: profiles.updatedAt,
      })
      .from(profiles)
      .orderBy(desc(profiles.createdAt))
      .limit(limit)
      .offset(offset);

    if (searchFilter) {
      profilesQuery.where(searchFilter);
    }

    const userProfiles = await profilesQuery;

    // Fetch real user data from Supabase auth if service role key is configured
    let authUserMap = new Map<string, { email: string; created_at: string; last_sign_in_at?: string }>()
    let usingRealEmails = false

    if (isAdminClientConfigured()) {
      try {
        const userIds = userProfiles.map(profile => profile.id)
        authUserMap = await fetchUsersFromAuth(userIds)
        usingRealEmails = true
      } catch (error) {
        console.warn('Failed to fetch real user emails, falling back to placeholders:', error)
      }
    }

    // Create user data combining profiles with auth data
    let users: UserData[] = userProfiles.map(profile => {
      const authUser = authUserMap.get(profile.id)

      return {
        id: profile.id,
        email: authUser?.email || `user_${profile.id.slice(0, 8)}@example.com`,
        createdAt: profile.createdAt || new Date().toISOString(),
        last_sign_in_at: authUser?.last_sign_in_at || null,
        created_at: authUser?.created_at || profile.createdAt || new Date().toISOString(),
        profile: {
          firstName: profile.firstName,
          lastName: profile.lastName,
          city: profile.city,
          state: profile.state,
          country: profile.country,
          isAdmin: profile.isAdmin,
          createdAt: profile.createdAt,
          updatedAt: profile.updatedAt,
        },
      }
    });

    // If we have real emails and a search query, filter by email as well
    if (search && usingRealEmails) {
      users = users.filter(user => {
        const searchLower = search.toLowerCase()
        return (
          user.email.toLowerCase().includes(searchLower) ||
          user.profile?.firstName?.toLowerCase().includes(searchLower) ||
          user.profile?.lastName?.toLowerCase().includes(searchLower) ||
          user.profile?.city?.toLowerCase().includes(searchLower)
        )
      })
    }

    // Calculate stats based on profiles
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    const newSignupsThisWeek = userProfiles.filter(profile => {
      if (!profile.createdAt) return false;
      return new Date(profile.createdAt) > oneWeekAgo;
    }).length;

    // For active users, we'll consider all non-admin users as potentially active
    // In production, you'd track last login times
    const activeUsers = Math.floor(totalUsers * 0.7); // Estimate 70% active

    return NextResponse.json({
      users,
      pagination: {
        page,
        limit,
        total: totalUsers,
        totalPages: Math.ceil(totalUsers / limit),
      },
      stats: {
        totalUsers,
        activeUsers,
        newSignupsThisWeek,
      },
      notice: usingRealEmails
        ? 'Displaying real user email addresses from Supabase Auth.'
        : 'Email addresses are placeholders. To display real emails, configure SUPABASE_SERVICE_ROLE_KEY.',
      usingRealEmails,
    });
  } catch (error) {
    console.error('Error in admin users route:', error);
    
    if (error instanceof Error) {
      if (error.message === 'Unauthorized') {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }
      if (error.message === 'Forbidden: Admin access required') {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }
    }
    
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}